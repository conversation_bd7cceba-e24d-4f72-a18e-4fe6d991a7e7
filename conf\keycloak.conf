# =============================================================================
# Minimal JSON Logging Configuration
# =============================================================================

# Enable JSON logging for console and file
log=console,file
log-console-output=json
log-file-output=json

# Configure log file location
log-file=log/keycloak.json

# Minimal JSON configuration - exclude unnecessary fields
log-console-json-excluded-keys=sequence,loggerClassName,threadId,mdc,ndc,hostName,processName,processId
log-file-json-excluded-keys=sequence,loggerClassName,threadId,mdc,ndc,hostName,processName,processId

# Minimal log levels - only essential events
log-level=WARN
log-level-org.keycloak=INFO
log-level-org.keycloak.events=INFO
log-level-org.keycloak.authentication=INFO
log-level-io.quarkus=WARN

# Suppress all verbose infrastructure categories
log-level-org.hibernate=OFF
log-level-org.infinispan=OFF
log-level-org.jgroups=OFF
log-level-org.jboss=OFF

# Database

# The database vendor.
db=mssql

# The username of the database user.
db-username=sa
#db-username=timur

# The password of the database user.
db-password=M1t1g@t0r
#db-password=T!mur123

# The full database JDBC URL. If not provided, a default URL is set based on the selected database vendor.
db-url=******************************************************************************
#db-url=***************************************************************************

# Observability

# If the server should expose healthcheck endpoints.
#health-enabled=true

# If the server should expose metrics endpoints.
#metrics-enabled=true

# HTTP
https-port=9443

# The file path to a server certificate or certificate chain in PEM format.
#https-certificate-file=C:/Mobile Aspects/curr/conf/cch9appvm01_mobileaspectshealth_org_cert.pem
https-certificate-file=C:/Maspects/CCH9/conf/kc.crt.pem

# The file path to a private key in PEM format.
#https-certificate-key-file=C:/Mobile Aspects/curr/conf/cch9appvm01_mobileaspectshealth_org_key.pem
https-certificate-key-file=C:/Maspects/CCH9/conf/kc.key.pem

# The proxy address forwarding mode if the server is behind a reverse proxy.
#proxy=reencrypt

# Do not attach route to cookies and rely on the session affinity capabilities from reverse proxy
#spi-sticky-session-encoder-infinispan-should-attach-route=false

# Hostname for the Keycloak server.
hostname=************
#hostname=************
#hostname=**************

hostname-strict=false

#hostname-strict-https=false
