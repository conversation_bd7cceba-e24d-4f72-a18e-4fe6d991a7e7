/*
 Created by  : <PERSON><PERSON><PERSON>
 Created On  : 08/05/2025
 Description : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> CUSTOM LOGIN THEME v26.2.0
		This theme customizes the Keycloak login page to:
		- Hide traditional username/password login
		- Display Mobile Aspects branding
		- Style Microsoft social login button
		- Apply custom color scheme and layout
 */

/* Main page background styling */
.login-pf body {
    background: #152935;           
    background-size: cover;        
    height: 100%;                  
}

/* Login page header spacing */
.login-pf-page .login-pf-page-header {
    margin-bottom: 0px;            /* Remove default bottom margin */
}

/* Mobile Aspects logo injection via CSS pseudo-element */
#kc-header-wrapper::before {
    content: "";                   
    display: block;                
    width: 300px;                  
    height: 80px;                  
    margin: 0 auto 15px auto;      
    background-image: url('../img/malogo.png');  /* Mobile Aspects logo */
    background-size: contain;      
    background-repeat: no-repeat;  
    background-position: center;   
}

/* Header wrapper container styling */
#kc-header-wrapper {
    text-align: center;            
    margin-bottom: 20px;           
}

/* Default brand logo styling */
.login-pf-page .login-pf-brand {
    margin-bottom: 30px;           
    text-align: center;            
}

/* Brand logo image constraints */
.login-pf-page .login-pf-brand img {
    max-width: 200px;              
    height: auto;                  
}

/* Hide traditional username/password login form */
#kc-form-login {
    display: none;                 /* Completely hide login form */
}


/* Social providers container styling */
#kc-social-providers {
    margin-top: 0 !important;      
    padding-top: 0 !important;     
    border-top: none !important;   
    background: none !important;   
}

/* Hide separator line between login forms and social providers */
#kc-social-providers hr {
    display: none !important;      
    margin: 0 !important;          
    padding: 0 !important;         
    border: none !important;       
}

/* Hide "Or sign in with" heading text */
#kc-social-providers h2 {
    display: none !important;      
    margin: 0 !important;          
    padding: 0 !important;         
}


/* Microsoft button padding and height adjustments */
#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
    padding-top: calc(0.5em + 5px) !important;    
    padding-bottom: calc(0.5em + 5px) !important; 
}

/* Microsoft button base styling */
#kc-social-providers .zocial.microsoft,
#kc-social-providers .btn.microsoft,
#kc-social-providers a[href*="microsoft"] {
    background-color: #0066cc !important;  
    color: white !important;               
    border: none !important;               
}

/* Microsoft button hover state styling */
#kc-social-providers .zocial.microsoft:hover,
#kc-social-providers .btn.microsoft:hover,
#kc-social-providers a[href*="microsoft"]:hover {
    background-color: #004080 !important;  
    color: white !important;               
}

/* Replace Font Awesome Windows icon with custom Mobile Aspects icon */
#kc-social-providers .kc-social-provider-link[href*="microsoft"] i,
#kc-social-providers a[href*="microsoft"] i,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fa,
#kc-social-providers a[href*="microsoft"] .fa,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fab,
#kc-social-providers a[href*="microsoft"] .fab,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fas,
#kc-social-providers a[href*="microsoft"] .fas {
    display: block !important;                        
    width: 40px !important;                           
    height: 40px !important;                          
    background-image: url('../img/maicon.png') !important; 
    background-size: contain !important;              
    background-repeat: no-repeat !important;          
    background-position: center !important;          
    text-indent: -9999px !important;                  
    font-size: 0 !important;                          
    color: transparent !important;                    
    overflow: hidden !important;                      
    position: absolute !important;                    
    left: 15px !important;                            
    top: 50% !important;                              
    transform: translateY(-50%) !important;           
}

/* Microsoft button layout and visual styling */
#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
    display: flex !important;              
    align-items: center !important;        
    justify-content: center !important;   
    position: relative !important;        
    text-align: center !important;         
    font-size: 20px;                       
    border-radius: 15px !important;        
    overflow: hidden !important;          
    border: none !important;               
    outline: none !important;             
}
