# =============================================================================
# Minimal JSON Logging Configuration for Keycloak
# =============================================================================

# Console JSON logging with minimal fields
quarkus.log.console.json=true
quarkus.log.console.json.pretty-print=false
quarkus.log.console.json.print-details=false
quarkus.log.console.json.excluded-keys=sequence,loggerClassName,threadId,mdc,ndc,hostName,processName,processId

# File JSON logging with minimal fields
quarkus.log.file.enable=true
quarkus.log.file.path=log/keycloak.json
quarkus.log.file.json=true
quarkus.log.file.json.pretty-print=false
quarkus.log.file.json.print-details=false
quarkus.log.file.json.excluded-keys=sequence,loggerClassName,threadId,mdc,ndc,hostName,processName,processId

# Minimal log levels - reduce verbosity
quarkus.log.level=WARN
quarkus.log.category."org.keycloak".level=INFO
quarkus.log.category."org.keycloak.events".level=INFO
quarkus.log.category."org.keycloak.authentication".level=INFO

# Suppress verbose categories to minimize log data
quarkus.log.category."org.hibernate".level=ERROR
quarkus.log.category."org.infinispan".level=ERROR
quarkus.log.category."org.jgroups".level=ERROR
quarkus.log.category."io.quarkus".level=WARN
quarkus.log.category."org.jboss".level=ERROR
