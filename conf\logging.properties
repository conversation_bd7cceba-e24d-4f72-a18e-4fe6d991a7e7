# =============================================================================
# Keycloak JSON Logging Configuration
# =============================================================================

# Console logging in JSON format
quarkus.log.console.json=true
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n
quarkus.log.console.json.pretty-print=false
quarkus.log.console.json.record-delimiter=\n

# File logging configuration
quarkus.log.file.enable=true
quarkus.log.file.path=log/keycloak.json
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n
quarkus.log.file.json=true
quarkus.log.file.json.pretty-print=false
quarkus.log.file.json.record-delimiter=\n

# File rotation settings
quarkus.log.file.rotation.max-file-size=10M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

# Log levels
quarkus.log.level=INFO
quarkus.log.category."org.keycloak".level=INFO
quarkus.log.category."org.jboss".level=WARN
quarkus.log.category."org.hibernate".level=WARN
quarkus.log.category."org.infinispan".level=WARN

# Security and audit logging
quarkus.log.category."org.keycloak.events".level=INFO
quarkus.log.category."org.keycloak.authentication".level=INFO
quarkus.log.category."org.keycloak.authorization".level=INFO

# Database logging (optional - uncomment if needed)
# quarkus.log.category."org.hibernate.SQL".level=DEBUG
# quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder".level=TRACE
