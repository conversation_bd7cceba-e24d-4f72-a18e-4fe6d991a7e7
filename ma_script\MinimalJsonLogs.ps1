# Keycloak Minimal JSON Log Processor
# This script processes Keycloak JSON logs to create minimal versions

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("process", "view", "monitor", "help")]
    [string]$Action = "help",
    
    [Parameter(Mandatory=$false)]
    [string]$InputFile = "log\keycloak.json",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = "log\keycloak-minimal.json",
    
    [Parameter(Mandatory=$false)]
    [int]$Lines = 50
)

$LogDir = "C:\Maspects\CCH9\log"
$SourceFile = "C:\Maspects\CCH9\$InputFile"
$TargetFile = "C:\Maspects\CCH9\$OutputFile"

function Show-Help {
    Write-Host "Keycloak Minimal JSON Log Processor" -ForegroundColor Green
    Write-Host "Usage: .\MinimalJsonLogs.ps1 -Action <action> [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Cyan
    Write-Host "  process  - Process full JSON logs to create minimal version"
    Write-Host "  view     - View recent minimal log entries"
    Write-Host "  monitor  - Monitor and process logs in real-time"
    Write-Host "  help     - Show this help message"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -InputFile <file>    Source JSON log file (default: log\keycloak.json)"
    Write-Host "  -OutputFile <file>   Target minimal JSON file (default: log\keycloak-minimal.json)"
    Write-Host "  -Lines <number>      Number of lines to show when viewing (default: 50)"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\MinimalJsonLogs.ps1 -Action process"
    Write-Host "  .\MinimalJsonLogs.ps1 -Action view -Lines 20"
    Write-Host "  .\MinimalJsonLogs.ps1 -Action monitor"
}

function Convert-ToMinimalJson {
    param([string]$JsonLine)
    
    try {
        $json = $JsonLine | ConvertFrom-Json
        
        # Create minimal JSON with only essential fields
        $minimal = @{
            timestamp = $json.timestamp
            level = $json.level
            logger = $json.loggerName
            message = $json.message
        }
        
        # Add thread name only if it's not "main"
        if ($json.threadName -and $json.threadName -ne "main") {
            $minimal.thread = $json.threadName
        }
        
        return ($minimal | ConvertTo-Json -Compress)
    } catch {
        # Return original line if JSON parsing fails
        return $JsonLine
    }
}

function Process-LogFile {
    if (-not (Test-Path $SourceFile)) {
        Write-Host "Source file not found: $SourceFile" -ForegroundColor Red
        return
    }
    
    Write-Host "Processing JSON logs to minimal format..." -ForegroundColor Green
    Write-Host "Source: $SourceFile" -ForegroundColor Gray
    Write-Host "Target: $TargetFile" -ForegroundColor Gray
    
    $lineCount = 0
    Get-Content $SourceFile | ForEach-Object {
        $minimalJson = Convert-ToMinimalJson $_
        $minimalJson | Out-File $TargetFile -Append -Encoding UTF8
        $lineCount++
        
        if ($lineCount % 100 -eq 0) {
            Write-Host "Processed $lineCount lines..." -ForegroundColor Yellow
        }
    }
    
    Write-Host "Processing complete. $lineCount lines processed." -ForegroundColor Green
    
    # Show file size comparison
    $sourceSize = (Get-Item $SourceFile).Length
    $targetSize = (Get-Item $TargetFile).Length
    $reduction = [math]::Round((($sourceSize - $targetSize) / $sourceSize) * 100, 1)
    
    Write-Host "Original size: $([math]::Round($sourceSize / 1KB, 1)) KB" -ForegroundColor Cyan
    Write-Host "Minimal size:  $([math]::Round($targetSize / 1KB, 1)) KB" -ForegroundColor Cyan
    Write-Host "Size reduction: $reduction%" -ForegroundColor Green
}

function View-MinimalLogs {
    if (-not (Test-Path $TargetFile)) {
        Write-Host "Minimal log file not found: $TargetFile" -ForegroundColor Red
        Write-Host "Run with -Action process first to create minimal logs." -ForegroundColor Yellow
        return
    }
    
    Write-Host "Showing last $Lines lines from minimal Keycloak log:" -ForegroundColor Green
    Get-Content $TargetFile -Tail $Lines | ForEach-Object {
        try {
            $json = $_ | ConvertFrom-Json
            $timestamp = $json.timestamp
            $level = $json.level
            $logger = $json.logger
            $message = $json.message
            $thread = if ($json.thread) { " ($($json.thread))" } else { "" }
            
            Write-Host "[$timestamp] $level [$logger]$thread - $message" -ForegroundColor $(
                switch ($level) {
                    "ERROR" { "Red" }
                    "WARN" { "Yellow" }
                    "INFO" { "White" }
                    default { "Gray" }
                }
            )
        } catch {
            Write-Host $_ -ForegroundColor Gray
        }
    }
}

function Monitor-Logs {
    Write-Host "Monitoring Keycloak logs and creating minimal versions..." -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor Yellow
    
    if (-not (Test-Path $SourceFile)) {
        Write-Host "Source file not found: $SourceFile" -ForegroundColor Red
        return
    }
    
    # Clear the minimal log file
    "" | Out-File $TargetFile -Encoding UTF8
    
    Get-Content $SourceFile -Wait -Tail 0 | ForEach-Object {
        $minimalJson = Convert-ToMinimalJson $_
        $minimalJson | Out-File $TargetFile -Append -Encoding UTF8
        
        # Display the minimal version
        try {
            $json = $minimalJson | ConvertFrom-Json
            $timestamp = $json.timestamp
            $level = $json.level
            $logger = $json.logger
            $message = $json.message
            $thread = if ($json.thread) { " ($($json.thread))" } else { "" }
            
            Write-Host "[$timestamp] $level [$logger]$thread - $message" -ForegroundColor $(
                switch ($level) {
                    "ERROR" { "Red" }
                    "WARN" { "Yellow" }
                    "INFO" { "White" }
                    default { "Gray" }
                }
            )
        } catch {
            Write-Host $_ -ForegroundColor Gray
        }
    }
}

# Main execution
switch ($Action) {
    "process" { Process-LogFile }
    "view" { View-MinimalLogs }
    "monitor" { Monitor-Logs }
    "help" { Show-Help }
    default { Show-Help }
}
