# Keycloak JSON Log Management Script
# This script helps manage Keycloak JSON logs

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("view", "tail", "search", "rotate", "clean", "help")]
    [string]$Action = "help",
    
    [Parameter(Mandatory=$false)]
    [string]$SearchTerm = "",
    
    [Parameter(Mandatory=$false)]
    [int]$Lines = 50,
    
    [Parameter(Mandatory=$false)]
    [int]$DaysToKeep = 30
)

$LogDir = "C:\Maspects\CCH9\log"
$LogFile = "$LogDir\keycloak.json"

function Show-Help {
    Write-Host "Keycloak JSON Log Management Script" -ForegroundColor Green
    Write-Host "Usage: .\ManageKeycloakLogs.ps1 -Action <action> [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Cyan
    Write-Host "  view     - View recent log entries (default: 50 lines)"
    Write-Host "  tail     - Monitor log file in real-time"
    Write-Host "  search   - Search for specific terms in logs"
    Write-Host "  rotate   - Manually rotate current log file"
    Write-Host "  clean    - Clean old log files (default: keep 30 days)"
    Write-Host "  help     - Show this help message"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -Lines <number>      Number of lines to show (default: 50)"
    Write-Host "  -SearchTerm <term>   Term to search for in logs"
    Write-Host "  -DaysToKeep <days>   Days of logs to keep when cleaning (default: 30)"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\ManageKeycloakLogs.ps1 -Action view -Lines 100"
    Write-Host "  .\ManageKeycloakLogs.ps1 -Action search -SearchTerm 'ERROR'"
    Write-Host "  .\ManageKeycloakLogs.ps1 -Action clean -DaysToKeep 7"
}

function View-Logs {
    if (Test-Path $LogFile) {
        Write-Host "Showing last $Lines lines from Keycloak log:" -ForegroundColor Green
        Get-Content $LogFile -Tail $Lines | ForEach-Object {
            try {
                $json = $_ | ConvertFrom-Json
                $timestamp = $json.timestamp
                $level = $json.level
                $message = $json.message
                Write-Host "[$timestamp] $level - $message" -ForegroundColor $(
                    switch ($level) {
                        "ERROR" { "Red" }
                        "WARN" { "Yellow" }
                        "INFO" { "White" }
                        default { "Gray" }
                    }
                )
            } catch {
                Write-Host $_ -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Log file not found: $LogFile" -ForegroundColor Red
    }
}

function Tail-Logs {
    if (Test-Path $LogFile) {
        Write-Host "Monitoring Keycloak log file (Press Ctrl+C to stop):" -ForegroundColor Green
        Get-Content $LogFile -Wait -Tail 10 | ForEach-Object {
            try {
                $json = $_ | ConvertFrom-Json
                $timestamp = $json.timestamp
                $level = $json.level
                $message = $json.message
                Write-Host "[$timestamp] $level - $message" -ForegroundColor $(
                    switch ($level) {
                        "ERROR" { "Red" }
                        "WARN" { "Yellow" }
                        "INFO" { "White" }
                        default { "Gray" }
                    }
                )
            } catch {
                Write-Host $_ -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Log file not found: $LogFile" -ForegroundColor Red
    }
}

function Search-Logs {
    if (-not $SearchTerm) {
        Write-Host "Please provide a search term using -SearchTerm parameter" -ForegroundColor Red
        return
    }
    
    if (Test-Path $LogFile) {
        Write-Host "Searching for '$SearchTerm' in Keycloak logs:" -ForegroundColor Green
        Get-Content $LogFile | Where-Object { $_ -match $SearchTerm } | ForEach-Object {
            try {
                $json = $_ | ConvertFrom-Json
                $timestamp = $json.timestamp
                $level = $json.level
                $message = $json.message
                Write-Host "[$timestamp] $level - $message" -ForegroundColor Yellow
            } catch {
                Write-Host $_ -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Log file not found: $LogFile" -ForegroundColor Red
    }
}

function Rotate-Logs {
    if (Test-Path $LogFile) {
        $timestamp = Get-Date -Format "yyyy-MM-dd-HHmmss"
        $rotatedFile = "$LogDir\keycloak.json.$timestamp"
        Move-Item $LogFile $rotatedFile
        Write-Host "Log file rotated to: $rotatedFile" -ForegroundColor Green
    } else {
        Write-Host "Log file not found: $LogFile" -ForegroundColor Red
    }
}

function Clean-Logs {
    $cutoffDate = (Get-Date).AddDays(-$DaysToKeep)
    $oldLogs = Get-ChildItem $LogDir -Filter "keycloak.json.*" | Where-Object { $_.LastWriteTime -lt $cutoffDate }
    
    if ($oldLogs.Count -gt 0) {
        Write-Host "Cleaning $($oldLogs.Count) old log files (older than $DaysToKeep days):" -ForegroundColor Green
        foreach ($log in $oldLogs) {
            Write-Host "  Removing: $($log.Name)" -ForegroundColor Yellow
            Remove-Item $log.FullName -Force
        }
        Write-Host "Cleanup completed." -ForegroundColor Green
    } else {
        Write-Host "No old log files found to clean." -ForegroundColor Green
    }
}

# Main execution
switch ($Action) {
    "view" { View-Logs }
    "tail" { Tail-Logs }
    "search" { Search-Logs }
    "rotate" { Rotate-Logs }
    "clean" { Clean-Logs }
    "help" { Show-Help }
    default { Show-Help }
}
