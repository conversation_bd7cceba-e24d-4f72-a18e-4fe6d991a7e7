﻿
$serviceName     = "MA_Keycloak"
$nssmPath        = "C:\Mobile Aspects\CCH9\ma_tools\nssm-2.24\win64\nssm.exe"                            
$keycloakBinPath = "C:\Mobile Aspects\CCH9\bin"
$kcBat           = Join-Path $keycloakBinPath "kc.bat"
$kcArgs          = "start"                                       

# --- CREATE SERVICE ---
Write-Host "Creating service '$serviceName' using NSSM..."

# Install service with executable and args
& "$nssmPath" install $serviceName $kcBat $kcArgs

# Set working directory
& "$nssmPath" set $serviceName AppDirectory $keycloakBinPath

# Optional: Set service startup type to auto-start
& "$nssmPath" set $serviceName Start SERVICE_AUTO_START

Write-Host "`nService '$serviceName' created successfully."
Write-Host "You can now run: Start-Service '$serviceName'"
